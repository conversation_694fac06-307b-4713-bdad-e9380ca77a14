import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Database file path
const DB_PATH = path.join(process.cwd(), 'data', 'store.db');

// Ensure data directory exists
const dataDir = path.dirname(DB_PATH);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Create database connection
let db: Database.Database | null = null;

export function getDatabase(): Database.Database {
  if (!db) {
    db = new Database(DB_PATH);
    
    // Enable foreign key constraints
    db.pragma('foreign_keys = ON');
    
    // Set journal mode to WAL for better performance
    db.pragma('journal_mode = WAL');
    
    // Initialize database schema
    initializeSchema();
  }
  
  return db;
}

function initializeSchema() {
  if (!db) return;
  
  const schemaPath = path.join(process.cwd(), 'lib', 'database', 'schema.sql');
  
  if (fs.existsSync(schemaPath)) {
    const schema = fs.readFileSync(schemaPath, 'utf-8');
    
    // Split schema by semicolons and execute each statement
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    for (const statement of statements) {
      try {
        db.exec(statement);
      } catch (error) {
        console.error('Error executing schema statement:', statement);
        console.error(error);
      }
    }
  }
}

// Close database connection
export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
  }
}

// Utility function to run migrations
export function runMigration(migrationSql: string) {
  const database = getDatabase();
  
  try {
    database.exec(migrationSql);
    console.log('Migration executed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Transaction wrapper
export function withTransaction<T>(callback: (db: Database.Database) => T): T {
  const database = getDatabase();
  
  const transaction = database.transaction(() => {
    return callback(database);
  });
  
  return transaction();
}

// Graceful shutdown
process.on('exit', closeDatabase);
process.on('SIGINT', closeDatabase);
process.on('SIGTERM', closeDatabase);
