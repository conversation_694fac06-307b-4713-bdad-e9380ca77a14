import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

const dataFilePath = path.join(process.cwd(), "data", "users.json")

// GET /api/admin/users
export async function GET() {
  try {
    const data = fs.readFileSync(dataFilePath, "utf-8")
    const users = JSON.parse(data)

    // Loại bỏ thông tin nhạy cảm như password
    const sanitizedUsers = users.map((user: any) => ({
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status || "active", // Thêm trạng thái mặc định nếu chưa có
      createdAt: user.createdAt,
      address: user.address,
    }))

    return NextResponse.json(sanitizedUsers)
  } catch (error) {
    console.error("Error reading users:", error)
    return NextResponse.json(
      { error: "Failed to read users" },
      { status: 500 }
    )
  }
}

// POST /api/admin/users
export async function POST(request: Request) {
  try {
    const data = fs.readFileSync(dataFilePath, "utf-8")
    const users = JSON.parse(data)

    const newUser = await request.json()

    // Validate required fields
    if (!newUser.name || !newUser.email || !newUser.password || !newUser.phone) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Check if email already exists
    if (users.some((user: any) => user.email === newUser.email)) {
      return NextResponse.json(
        { error: "Email already exists" },
        { status: 400 }
      )
    }

    // Generate new user ID
    const lastUser = users[users.length - 1]
    const lastId = lastUser ? parseInt(lastUser.id.split("-")[1]) : 0
    const newId = `user-${String(lastId + 1).padStart(3, "0")}`

    // Create new user object
    const userToAdd = {
      id: newId,
      name: newUser.name,
      email: newUser.email,
      password: newUser.password,
      phone: newUser.phone,
      role: newUser.role || "customer",
      status: "active",
      createdAt: new Date().toISOString(),
      address: newUser.address || null,
    }

    users.push(userToAdd)
    fs.writeFileSync(dataFilePath, JSON.stringify(users, null, 2))

    // Return sanitized user data
    const { password, ...sanitizedUser } = userToAdd
    return NextResponse.json(sanitizedUser, { status: 201 })
  } catch (error) {
    console.error("Error creating user:", error)
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/users/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status, role } = await request.json()

    const data = fs.readFileSync(dataFilePath, "utf-8")
    const users = JSON.parse(data)

    const userIndex = users.findIndex((u: any) => u.id === id)

    if (userIndex === -1) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Cập nhật thông tin người dùng
    users[userIndex] = {
      ...users[userIndex],
      ...(status && { status }),
      ...(role && { role }),
    }

    fs.writeFileSync(dataFilePath, JSON.stringify(users, null, 2))

    // Trả về thông tin đã được sanitize
    const updatedUser = {
      id: users[userIndex].id,
      name: users[userIndex].name,
      email: users[userIndex].email,
      phone: users[userIndex].phone,
      role: users[userIndex].role,
      status: users[userIndex].status || "active",
      createdAt: users[userIndex].createdAt,
      address: users[userIndex].address,
    }

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error("Error updating user:", error)
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/users/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const data = fs.readFileSync(dataFilePath, "utf-8")
    const users = JSON.parse(data)

    const userIndex = users.findIndex((u: any) => u.id === id)

    if (userIndex === -1) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Không cho phép xóa tài khoản admin
    if (users[userIndex].role === "admin") {
      return NextResponse.json(
        { error: "Cannot delete admin account" },
        { status: 403 }
      )
    }

    users.splice(userIndex, 1)

    fs.writeFileSync(dataFilePath, JSON.stringify(users, null, 2))

    return NextResponse.json({ message: "User deleted successfully" })
  } catch (error) {
    console.error("Error deleting user:", error)
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    )
  }
} 