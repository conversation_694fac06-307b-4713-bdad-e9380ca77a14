import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET(request: Request) {
  try {
    // Lấy userId từ query parameters
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Đọc file orders.json
    const filePath = path.join(process.cwd(), 'data', 'orders.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const orders = JSON.parse(fileContents)

    // Lọc đơn hàng theo userId
    const userOrders = orders.filter((order: any) => order.userId === userId)

    return NextResponse.json(userOrders)
  } catch (error) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const newOrder = await request.json()
    
    // Validate required fields
    if (!newOrder.userId || !newOrder.items || !newOrder.totalAmount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const filePath = path.join(process.cwd(), 'data', 'orders.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const orders = JSON.parse(fileContents)

    // Add new order
    orders.push(newOrder)

    // Write back to file
    fs.writeFileSync(filePath, JSON.stringify(orders, null, 2))

    return NextResponse.json(newOrder, { status: 201 })
  } catch (error) {
    console.error('Error adding order:', error)
    return NextResponse.json(
      { error: 'Failed to add order' },
      { status: 500 }
    )
  }
} 