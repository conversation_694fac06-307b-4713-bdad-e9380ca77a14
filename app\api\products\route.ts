import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

// Đường dẫn đến file products.json
const PRODUCTS_FILE_PATH = path.join(process.cwd(), "data", "products.json")

// Đ<PERSON><PERSON> dữ liệu products từ file
function readProducts() {
  try {
    const data = fs.readFileSync(PRODUCTS_FILE_PATH, "utf8")
    return JSON.parse(data)
  } catch (error) {
    return []
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const featured = searchParams.get("featured")
    const sort = searchParams.get("sort")
    const limit = parseInt(searchParams.get("limit") || "10")

    // Đọc danh sách sản phẩm
    let products = readProducts()

    // Lọc sản phẩm nổi bật nếu có yêu cầu
    if (featured === "true") {
      products = products.filter((product: any) => product.featured)
    }

    // Sắp xếp sản phẩm
    if (sort === "newest") {
      products.sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    }

    // Giới hạn số lượng sản phẩm
    products = products.slice(0, limit)

    return NextResponse.json(products)
  } catch (error) {
    console.error("Error fetching products:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách sản phẩm." },
      { status: 500 }
    )
  }
} 