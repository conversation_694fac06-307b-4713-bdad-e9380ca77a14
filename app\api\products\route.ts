import { NextResponse } from "next/server"
import { ProductModel, CategoryModel } from "../../../lib/database/models"

// Transform database product to API format
function transformProduct(dbProduct: any) {
  return {
    id: dbProduct.id,
    name: dbProduct.name,
    description: dbProduct.description,
    price: dbProduct.price,
    originalPrice: dbProduct.original_price,
    image: dbProduct.image,
    images: dbProduct.images ? JSON.parse(dbProduct.images) : [],
    category: {
      id: dbProduct.category_id,
      name: dbProduct.category_name || dbProduct.category_id,
      slug: dbProduct.category_id
    },
    stock: dbProduct.stock,
    rating: dbProduct.rating,
    reviewCount: dbProduct.review_count,
    soldCount: dbProduct.sold_count,
    featured: Boolean(dbProduct.featured),
    createdAt: dbProduct.created_at
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const featured = searchParams.get("featured")
    const sort = searchParams.get("sort")
    const limit = parseInt(searchParams.get("limit") || "10")
    const categoryId = searchParams.get("category")

    // Build query options
    const queryOptions: any = {
      limit,
      orderBy: 'created_at',
      orderDirection: 'DESC' as const
    }

    // Handle sorting
    if (sort === "newest") {
      queryOptions.orderBy = 'created_at'
      queryOptions.orderDirection = 'DESC'
    } else if (sort === "price_asc") {
      queryOptions.orderBy = 'price'
      queryOptions.orderDirection = 'ASC'
    } else if (sort === "price_desc") {
      queryOptions.orderBy = 'price'
      queryOptions.orderDirection = 'DESC'
    } else if (sort === "popular") {
      queryOptions.orderBy = 'sold_count'
      queryOptions.orderDirection = 'DESC'
    }

    // Get products from database
    let products = ProductModel.getAll({
      featured: featured === "true" ? true : undefined,
      categoryId: categoryId || undefined,
      ...queryOptions
    })

    // Transform products to API format
    const transformedProducts = products.map(transformProduct)

    return NextResponse.json(transformedProducts)
  } catch (error) {
    console.error("Error fetching products:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách sản phẩm." },
      { status: 500 }
    )
  }
}