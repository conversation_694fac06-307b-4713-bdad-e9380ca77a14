import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

const dataFilePath = path.join(process.cwd(), "data", "categories.json")

// GET /api/admin/categories
export async function GET() {
  try {
    const data = fs.readFileSync(dataFilePath, "utf-8")
    const categories = JSON.parse(data)
    return NextResponse.json(categories)
  } catch (error) {
    console.error("Error reading categories:", error)
    return NextResponse.json(
      { error: "Failed to read categories" },
      { status: 500 }
    )
  }
}

// POST /api/admin/categories
export async function POST(request: Request) {
  try {
    const data = fs.readFileSync(dataFilePath, "utf-8")
    const categories = JSON.parse(data)

    const newCategory = await request.json()

    // Validate required fields
    if (!newCategory.name || !newCategory.description || !newCategory.image) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Generate slug from name
    const slug = newCategory.name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[đĐ]/g, "d")
      .replace(/([^0-9a-z-\s])/g, "")
      .replace(/(\s+)/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-+|-+$/g, "")

    // Generate new category ID
    const lastCategory = categories[categories.length - 1]
    const lastId = lastCategory ? parseInt(lastCategory.id.split("-")[1]) : 0
    const newId = `cat-${String(lastId + 1).padStart(3, "0")}`

    // Create new category object
    const categoryToAdd = {
      id: newId,
      name: newCategory.name,
      slug,
      description: newCategory.description,
      image: newCategory.image,
      status: "active",
      createdAt: new Date().toISOString(),
    }

    categories.push(categoryToAdd)
    fs.writeFileSync(dataFilePath, JSON.stringify(categories, null, 2))

    return NextResponse.json(categoryToAdd, { status: 201 })
  } catch (error) {
    console.error("Error creating category:", error)
    return NextResponse.json(
      { error: "Failed to create category" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/categories/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status } = await request.json()

    const data = fs.readFileSync(dataFilePath, "utf-8")
    const categories = JSON.parse(data)

    const categoryIndex = categories.findIndex((c: any) => c.id === id)

    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      )
    }

    categories[categoryIndex] = {
      ...categories[categoryIndex],
      status,
    }

    fs.writeFileSync(dataFilePath, JSON.stringify(categories, null, 2))

    return NextResponse.json(categories[categoryIndex])
  } catch (error) {
    console.error("Error updating category:", error)
    return NextResponse.json(
      { error: "Failed to update category" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/categories/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const data = fs.readFileSync(dataFilePath, "utf-8")
    const categories = JSON.parse(data)

    const categoryIndex = categories.findIndex((c: any) => c.id === id)

    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      )
    }

    categories.splice(categoryIndex, 1)

    fs.writeFileSync(dataFilePath, JSON.stringify(categories, null, 2))

    return NextResponse.json({ message: "Category deleted successfully" })
  } catch (error) {
    console.error("Error deleting category:", error)
    return NextResponse.json(
      { error: "Failed to delete category" },
      { status: 500 }
    )
  }
} 