import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

// GET /api/admin/reviews
export async function GET() {
  try {
    const reviewsPath = path.join(process.cwd(), "data", "reviews.json")
    const reviews = JSON.parse(fs.readFileSync(reviewsPath, "utf8"))
    return NextResponse.json(reviews)
  } catch (error) {
    console.error("Error fetching reviews:", error)
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/reviews/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status } = await request.json()

    const reviewsPath = path.join(process.cwd(), "data", "reviews.json")
    const reviews = JSON.parse(fs.readFileSync(reviewsPath, "utf8"))

    const reviewIndex = reviews.findIndex((r: any) => r.id === id)
    if (reviewIndex === -1) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    reviews[reviewIndex].status = status
    fs.writeFileSync(reviewsPath, JSON.stringify(reviews, null, 2))

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating review:", error)
    return NextResponse.json(
      { error: "Failed to update review" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/reviews/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const reviewsPath = path.join(process.cwd(), "data", "reviews.json")
    const reviews = JSON.parse(fs.readFileSync(reviewsPath, "utf8"))

    const reviewIndex = reviews.findIndex((r: any) => r.id === id)
    if (reviewIndex === -1) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    reviews.splice(reviewIndex, 1)
    fs.writeFileSync(reviewsPath, JSON.stringify(reviews, null, 2))

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting review:", error)
    return NextResponse.json(
      { error: "Failed to delete review" },
      { status: 500 }
    )
  }
} 