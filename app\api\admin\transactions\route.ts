import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

const dataFilePath = path.join(process.cwd(), "data", "transactions.json")

// GET /api/admin/transactions
export async function GET() {
  try {
    const data = fs.readFileSync(dataFilePath, "utf-8")
    const transactions = JSON.parse(data)

    // Đọ<PERSON> thêm thông tin từ orders.json và users.json để bổ sung thông tin
    const ordersData = fs.readFileSync(
      path.join(process.cwd(), "data", "orders.json"),
      "utf-8"
    )
    const usersData = fs.readFileSync(
      path.join(process.cwd(), "data", "users.json"),
      "utf-8"
    )
    const orders = JSON.parse(ordersData)
    const users = JSON.parse(usersData)

    // Kết hợp thông tin
    const enrichedTransactions = transactions.map((transaction: any) => {
      const order = orders.find((o: any) => o.id === transaction.orderId)
      const user = users.find((u: any) => u.id === transaction.userId)

      return {
        ...transaction,
        order: order
          ? {
              id: order.id,
              status: order.status,
              totalAmount: order.totalAmount,
            }
          : null,
        user: user
          ? {
              id: user.id,
              name: user.name,
              email: user.email,
            }
          : null,
      }
    })

    return NextResponse.json(enrichedTransactions)
  } catch (error) {
    console.error("Error reading transactions:", error)
    return NextResponse.json(
      { error: "Failed to read transactions" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/transactions/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status } = await request.json()

    const data = fs.readFileSync(dataFilePath, "utf-8")
    const transactions = JSON.parse(data)

    const transactionIndex = transactions.findIndex(
      (t: any) => t.id === id
    )

    if (transactionIndex === -1) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      )
    }

    transactions[transactionIndex] = {
      ...transactions[transactionIndex],
      status,
    }

    fs.writeFileSync(dataFilePath, JSON.stringify(transactions, null, 2))

    return NextResponse.json(transactions[transactionIndex])
  } catch (error) {
    console.error("Error updating transaction:", error)
    return NextResponse.json(
      { error: "Failed to update transaction" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/transactions/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const data = fs.readFileSync(dataFilePath, "utf-8")
    const transactions = JSON.parse(data)

    const transactionIndex = transactions.findIndex(
      (t: any) => t.id === id
    )

    if (transactionIndex === -1) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      )
    }

    transactions.splice(transactionIndex, 1)

    fs.writeFileSync(dataFilePath, JSON.stringify(transactions, null, 2))

    return NextResponse.json({ message: "Transaction deleted successfully" })
  } catch (error) {
    console.error("Error deleting transaction:", error)
    return NextResponse.json(
      { error: "Failed to delete transaction" },
      { status: 500 }
    )
  }
} 