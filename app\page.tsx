"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ShoppingBag, User, LogOut, Package, Settings } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Category {
  id: string
  name: string
  slug: string
}

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice: number
  image: string
  images: string[]
  category: Category
  stock: number
  rating: number
  reviewCount: number
  soldCount: number
  featured?: boolean
  promotional?: boolean
  createdAt: string
}

export default function HomePage() {
  const [user, setUser] = useState<User | null>(null)
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [newArrivals, setNewArrivals] = useState<Product[]>([])
  const [promotionalProducts, setPromotionalProducts] = useState<Product[]>([])
  const router = useRouter()

  useEffect(() => {
    // Lấy thông tin user từ localStorage
    const userStr = localStorage.getItem("user")
    if (userStr) {
      setUser(JSON.parse(userStr))
      // Nếu đã đăng nhập, chuyển hướng đến trang chủ cho người dùng đã đăng nhập
      router.push("/dashboard")
    }

    // Lấy dữ liệu sản phẩm
    const fetchProducts = async () => {
      try {
        // Lấy sản phẩm nổi bật
        const featuredResponse = await fetch('/api/products?featured=true&limit=4')
        const featuredData = await featuredResponse.json()
        setFeaturedProducts(featuredData)

        // Lấy sản phẩm mới
        const newArrivalsResponse = await fetch('/api/products?sort=newest&limit=4')
        const newArrivalsData = await newArrivalsResponse.json()
        setNewArrivals(newArrivalsData)

        // Lấy sản phẩm khuyến mãi
        const promotionalResponse = await fetch('/api/products/promotional?limit=4')
        const promotionalData = await promotionalResponse.json()
        setPromotionalProducts(promotionalData)
  } catch (error) {
        console.error('Error fetching products:', error)
      }
    }

    fetchProducts()
  }, [router])

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between px-8 md:px-12 lg:px-20">
          <Link href="/" className="flex items-center gap-2 font-bold text-2xl">
            <ShoppingBag className="h-6 w-6" />
            <span>TechStore</span>
          </Link>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button variant="ghost" asChild>
                <Link href="/login">Đăng nhập</Link>
              </Button>
              <Button asChild>
                <Link href="/register">Đăng ký</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-8 md:px-12 lg:px-20">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                    Chào mừng đến với TechStore
                  </h1>
                  <p className="max-w-[600px] text-muted-foreground md:text-xl">
                    Khám phá thế giới công nghệ với những sản phẩm chất lượng cao và giá cả phải chăng.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Button asChild>
                    <Link href="/products">Xem sản phẩm</Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/register">Tạo tài khoản</Link>
                    </Button>
                </div>
              </div>
              <div className="flex items-center justify-center">
                      <img
                  src="/placeholder.svg"
                  alt="Hero"
                  className="aspect-video overflow-hidden rounded-xl object-cover object-center"
                  width="600"
                  height="400"
                      />
              </div>
            </div>
          </div>
        </section>
        
        <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
          <div className="container px-8 md:px-12 lg:px-20">
            <h2 className="text-3xl font-bold tracking-tighter mb-8">Sản phẩm nổi bật</h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {featuredProducts.map((product) => (
                <Card key={product.id}>
                  <CardHeader>
                    <img
                      src={product.image}
                      alt={product.name}
                      className="aspect-square object-cover rounded-lg"
                    />
                    <CardTitle>{product.name}</CardTitle>
                    <CardDescription>{product.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{product.price.toLocaleString('vi-VN')}đ</p>
                    <Button className="w-full mt-4" asChild>
                      <Link href={`/products/${product.id}`}>Xem chi tiết</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-8 md:px-12 lg:px-20">
            <h2 className="text-3xl font-bold tracking-tighter mb-8">Sản phẩm mới</h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {newArrivals.map((product) => (
                <Card key={product.id}>
                  <CardHeader>
                    <img
                      src={product.image}
                      alt={product.name}
                      className="aspect-square object-cover rounded-lg"
                    />
                    <CardTitle>{product.name}</CardTitle>
                    <CardDescription>{product.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{product.price.toLocaleString('vi-VN')}đ</p>
                    <Button className="w-full mt-4" asChild>
                      <Link href={`/products/${product.id}`}>Xem chi tiết</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
          <div className="container px-8 md:px-12 lg:px-20">
            <h2 className="text-3xl font-bold tracking-tighter mb-8">Khuyến mãi hấp dẫn</h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {promotionalProducts.map((product) => (
                <Card key={product.id}>
                  <CardHeader>
                    <img
                      src={product.image}
                      alt={product.name}
                      className="aspect-square object-cover rounded-lg"
                    />
                    <CardTitle>{product.name}</CardTitle>
                    <CardDescription>{product.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{product.price.toLocaleString('vi-VN')}đ</p>
                    <Button className="w-full mt-4" asChild>
                      <Link href={`/products/${product.id}`}>Xem chi tiết</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>

      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row px-8 md:px-12 lg:px-20">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            Built by{" "}
            <a
              href="#"
              target="_blank"
              rel="noreferrer"
              className="font-medium underline underline-offset-4"
            >
              TechStore
            </a>
            . All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
