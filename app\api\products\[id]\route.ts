import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

const productsPath = path.join(process.cwd(), "data", "products.json")

// Đ<PERSON><PERSON> dữ liệu products từ file
function readProducts() {
  try {
    const data = fs.readFileSync(productsPath, "utf8")
    return JSON.parse(data)
  } catch (error) {
    return []
  }
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const products = readProducts()

    const product = products.find((p: any) => String(p.id) === String(id))

    if (!product) {
      return NextResponse.json(
        { error: "Sản phẩm không tìm thấy." },
        { status: 404 }
      )
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error("Error fetching product details:", error)
    return NextResponse.json(
      { error: "<PERSON>ó lỗi xảy ra khi lấy chi tiết sản phẩm." },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const products = readProducts()
    const updatedProductData = await request.json()

    const productIndex = products.findIndex((p: any) => String(p.id) === String(id))

    if (productIndex === -1) {
      return NextResponse.json(
        { error: "Sản phẩm không tìm thấy." },
        { status: 404 }
      )
    }

    // Cập nhật chỉ các trường được gửi trong request body
    products[productIndex] = { ...products[productIndex], ...updatedProductData }

    fs.writeFileSync(productsPath, JSON.stringify(products, null, 2), "utf8")

    return NextResponse.json({ message: "Cập nhật sản phẩm thành công." })
  } catch (error) {
    console.error("Error updating product:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật sản phẩm." },
      { status: 500 }
    )
  }
}