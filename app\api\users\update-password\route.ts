import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function PUT(request: Request) {
  try {
    const data = await request.json()
    const { id, currentPassword, newPassword } = data

    // Đọc file users.json
    const filePath = path.join(process.cwd(), 'data', 'users.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const users = JSON.parse(fileContents)

    // Tìm người dùng
    const userIndex = users.findIndex((user: any) => user.id === id)
    if (userIndex === -1) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Kiểm tra mật khẩu hiện tại
    if (users[userIndex].password !== currentPassword) {
      return NextResponse.json(
        { message: '<PERSON><PERSON>t khẩu hiện tại không đúng' },
        { status: 400 }
      )
    }

    // C<PERSON><PERSON> nhật mật khẩu mới
    users[userIndex].password = newPassword

    // Lưu lại vào file
    fs.writeFileSync(filePath, JSON.stringify(users, null, 2))

    return NextResponse.json({ message: 'Password updated successfully' })
  } catch (error) {
    console.error('Error updating password:', error)
    return NextResponse.json(
      { error: 'Failed to update password' },
      { status: 500 }
    )
  }
} 