import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json()

    // Đọc file users.json
    const filePath = path.join(process.cwd(), 'data', 'users.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const users = JSON.parse(fileContents)

    // Tìm user theo email
    const user = users.find((u: any) => u.email === email)

    // Kiểm tra user tồn tại và mật khẩu đúng
    if (!user || user.password !== password) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Trả về thông tin user (không bao gồm mật khẩu)
    const { password: _, ...userWithoutPassword } = user
    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'Failed to login' },
      { status: 500 }
    )
  }
} 