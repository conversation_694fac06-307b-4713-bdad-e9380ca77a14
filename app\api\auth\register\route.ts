import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

// Đường dẫn đến file users.json
const USERS_FILE_PATH = path.join(process.cwd(), "data", "users.json")

// Đọc dữ liệu users từ file
function readUsers() {
  try {
    const data = fs.readFileSync(USERS_FILE_PATH, "utf8")
    return JSON.parse(data)
  } catch (error) {
    return []
  }
}

// Ghi dữ liệu users vào file
function writeUsers(users: any[]) {
  try {
    fs.writeFileSync(USERS_FILE_PATH, JSON.stringify(users, null, 2))
    return true
  } catch (error) {
    return false
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { name, email, password } = body

    // Kiểm tra các trường bắt buộc
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: "<PERSON><PERSON> lòng điền đầy đủ thông tin." },
        { status: 400 }
      )
    }

    // Đ<PERSON><PERSON> danh sách users hiện tại
    const users = readUsers()

    // <PERSON>ểm tra email đã tồn tại chưa
    const existingUser = users.find((user: any) => user.email === email)
    if (existingUser) {
      return NextResponse.json(
        { error: "Email này đã được sử dụng." },
        { status: 400 }
      )
    }

    // Tạo user mới
    const newUser = {
      id: `user-${String(users.length + 1).padStart(3, "0")}`,
      name,
      email,
      password,
      phone: "",
      role: "customer",
      createdAt: new Date().toISOString(),
    }

    // Thêm user mới vào danh sách
    users.push(newUser)

    // Lưu danh sách users mới vào file
    const success = writeUsers(users)
    if (!success) {
      return NextResponse.json(
        { error: "Có lỗi xảy ra khi lưu thông tin." },
        { status: 500 }
      )
    }

    // Trả về thông tin user mới (không bao gồm password)
    const { password: _, ...userWithoutPassword } = newUser
    return NextResponse.json(userWithoutPassword)
  } catch (error) {
    console.error("Registration error:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi đăng ký." },
      { status: 500 }
    )
  }
} 