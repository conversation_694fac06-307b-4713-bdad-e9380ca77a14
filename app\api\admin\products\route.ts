import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

const productsFilePath = path.join(process.cwd(), "data", "products.json")
const promotionalProductsFilePath = path.join(process.cwd(), "data", "promotionalProducts.json")

// GET /api/admin/products
export async function GET() {
  try {
    const productsData = fs.readFileSync(productsFilePath, "utf-8")
    const promotionalData = fs.readFileSync(promotionalProductsFilePath, "utf-8")
    const products = JSON.parse(productsData)
    const promotionalProducts = JSON.parse(promotionalData)

    // Kết hợp danh sách sản phẩm
    const allProducts = [...products, ...promotionalProducts]

    return NextResponse.json(allProducts)
  } catch (error) {
    console.error("Error reading products:", error)
    return NextResponse.json(
      { error: "Failed to read products" },
      { status: 500 }
    )
  }
}

// POST /api/admin/products
export async function POST(request: Request) {
  try {
    const data = fs.readFileSync(productsFilePath, "utf-8")
    const products = JSON.parse(data)

    const newProduct = await request.json()

    // Validate required fields
    if (!newProduct.name || !newProduct.price || !newProduct.categoryId || !newProduct.image) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Generate slug from name
    const slug = newProduct.name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[đĐ]/g, "d")
      .replace(/([^0-9a-z-\s])/g, "")
      .replace(/(\s+)/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-+|-+$/g, "")

    // Generate new product ID
    const lastProduct = products[products.length - 1]
    const lastId = lastProduct ? parseInt(lastProduct.id.split("-")[1]) : 0
    const newId = `prod-${String(lastId + 1).padStart(3, "0")}`

    // Create new product object
    const productToAdd = {
      id: newId,
      name: newProduct.name,
      slug,
      description: newProduct.description || "",
      price: newProduct.price,
      categoryId: newProduct.categoryId,
      images: [newProduct.image],
      status: "active",
      stock: newProduct.stock || 0,
      createdAt: new Date().toISOString(),
      specifications: newProduct.specifications || {},
      features: newProduct.features || [],
    }

    products.push(productToAdd)
    fs.writeFileSync(productsFilePath, JSON.stringify(products, null, 2))

    return NextResponse.json(productToAdd, { status: 201 })
  } catch (error) {
    console.error("Error creating product:", error)
    return NextResponse.json(
      { error: "Failed to create product" },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/products/:id
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { status } = await request.json()

    const productsData = fs.readFileSync(productsFilePath, "utf-8")
    const promotionalData = fs.readFileSync(promotionalProductsFilePath, "utf-8")
    const products = JSON.parse(productsData)
    const promotionalProducts = JSON.parse(promotionalData)

    // Tìm sản phẩm trong cả hai danh sách
    const productIndex = products.findIndex((p: any) => p.id === id)
    const promotionalIndex = promotionalProducts.findIndex((p: any) => p.id === id)

    if (productIndex === -1 && promotionalIndex === -1) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    // Cập nhật trạng thái sản phẩm
    if (productIndex !== -1) {
      products[productIndex] = {
        ...products[productIndex],
        status,
      }
      fs.writeFileSync(productsFilePath, JSON.stringify(products, null, 2))
      return NextResponse.json(products[productIndex])
    } else {
      promotionalProducts[promotionalIndex] = {
        ...promotionalProducts[promotionalIndex],
        status,
      }
      fs.writeFileSync(promotionalProductsFilePath, JSON.stringify(promotionalProducts, null, 2))
      return NextResponse.json(promotionalProducts[promotionalIndex])
    }
  } catch (error) {
    console.error("Error updating product:", error)
    return NextResponse.json(
      { error: "Failed to update product" },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/products/:id
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const productsData = fs.readFileSync(productsFilePath, "utf-8")
    const promotionalData = fs.readFileSync(promotionalProductsFilePath, "utf-8")
    const products = JSON.parse(productsData)
    const promotionalProducts = JSON.parse(promotionalData)

    // Tìm sản phẩm trong cả hai danh sách
    const productIndex = products.findIndex((p: any) => p.id === id)
    const promotionalIndex = promotionalProducts.findIndex((p: any) => p.id === id)

    if (productIndex === -1 && promotionalIndex === -1) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    // Xóa sản phẩm
    if (productIndex !== -1) {
      products.splice(productIndex, 1)
      fs.writeFileSync(productsFilePath, JSON.stringify(products, null, 2))
    } else {
      promotionalProducts.splice(promotionalIndex, 1)
      fs.writeFileSync(promotionalProductsFilePath, JSON.stringify(promotionalProducts, null, 2))
    }

    return NextResponse.json({ message: "Product deleted successfully" })
  } catch (error) {
    console.error("Error deleting product:", error)
    return NextResponse.json(
      { error: "Failed to delete product" },
      { status: 500 }
    )
  }
} 