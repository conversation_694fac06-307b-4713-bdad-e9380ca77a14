import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function PUT(request: Request) {
  try {
    const data = await request.json()
    const { id, name, email, phone, address } = data

    // Đọc file users.json
    const filePath = path.join(process.cwd(), 'data', 'users.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const users = JSON.parse(fileContents)

    // Tìm và cập nhật thông tin người dùng
    const userIndex = users.findIndex((user: any) => user.id === id)
    if (userIndex === -1) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Cập nhật thông tin
    users[userIndex] = {
      ...users[userIndex],
      name,
      email,
      phone,
      address,
    }

    // Lưu lại vào file
    fs.writeFileSync(filePath, JSON.stringify(users, null, 2))

    // Tr<PERSON> về thông tin người dùng đã cập nhật
    return NextResponse.json(users[userIndex])
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
} 