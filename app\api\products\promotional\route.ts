import { NextResponse } from "next/server"
import fs from "fs"
import path from "path"

// Đường dẫn đến file promotionalProducts.json
const PROMOTIONAL_PRODUCTS_FILE_PATH = path.join(process.cwd(), "data", "promotionalProducts.json")

// Đ<PERSON>c dữ liệu promotional products từ file
function readPromotionalProducts() {
  try {
    const data = fs.readFileSync(PROMOTIONAL_PRODUCTS_FILE_PATH, "utf8")
    return JSON.parse(data)
  } catch (error) {
    return []
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get("limit") || "10")

    // Đọc danh sách sản phẩm khuyến mãi
    let products = readPromotionalProducts()

    // Giới hạn số lượng sản phẩm
    products = products.slice(0, limit)

    return NextResponse.json(products)
  } catch (error) {
    console.error("Error fetching promotional products:", error)
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách sản phẩm khuyến mãi." },
      { status: 500 }
    )
  }
} 