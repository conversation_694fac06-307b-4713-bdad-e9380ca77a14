"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { ShoppingBag, User, LogOut, Package, Settings, Star, ShoppingCart, Heart, ChevronLeft, ChevronRight } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import DashboardLayout from "@/app/dashboard/layout"

interface User {
  id: string
  name: string
  email: string
  role: string
  phone: string
  address?: {
    fullName: string
    phone: string
    address: string
    city: string
    district: string
    ward: string
  }
}

interface Category {
  id: string
  name: string
  slug: string
}

interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice: number
  image: string
  images: string[]
  category: Category
  stock: number
  rating: number
  reviewCount: number
  soldCount: number
  featured: boolean
  createdAt: string
}

export default function ProductDetailPage() {
  const [user, setUser] = useState<User | null>(null)
  const [product, setProduct] = useState<Product | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState(0)
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      try {
        setUser(JSON.parse(userData))
      } catch (error) {
        console.error('Error parsing user data:', error)
        localStorage.removeItem("user")
      }
    }

    const fetchProduct = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const productId = params?.id
        if (!productId || typeof productId !== 'string') {
          throw new Error('Invalid product ID')
        }

        const response = await fetch(`/api/products/${productId}`)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch product')
        }

        setProduct(data)
      } catch (error) {
        console.error('Error fetching product:', error)
        setError(error instanceof Error ? error.message : 'An error occurred')
        toast({
          title: "Lỗi",
          description: error instanceof Error ? error.message : 'Không thể tải thông tin sản phẩm',
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [params?.id, router, toast])

  const handleLogout = () => {
    localStorage.removeItem("user")
    setUser(null)
    router.push("/")
  }

  const handleQuantityChange = (value: number) => {
    if (value >= 1 && value <= (product?.stock || 1)) {
      setQuantity(value)
    }
  }

  const handleAddToCart = async () => {
    if (!user) {
      toast({
        title: "Yêu cầu đăng nhập",
        description: "Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng.",
        variant: "destructive",
      })
      router.push('/login')
      return
    }

    if (!product) {
      toast({
        title: "Lỗi",
        description: "Không thể thêm sản phẩm vào giỏ hàng.",
        variant: "destructive",
      })
      return
    }

    // Kiểm tra số lượng tồn kho
    if (quantity > product.stock) {
      toast({
        title: "Không đủ hàng",
        description: `Chỉ còn ${product.stock} sản phẩm trong kho.`,
        variant: "destructive",
      })
      return
    }

    try {
      // Tạo đơn hàng mới
      const newOrder = {
        id: `order-${Date.now()}`,
        userId: user.id,
        items: [
          {
            id: `order-item-${Date.now()}`,
            orderId: `order-${Date.now()}`,
            productId: product.id,
            product: {
              id: product.id,
              name: product.name,
              price: product.price,
              image: product.image
            },
            quantity: quantity,
            price: product.price * quantity
          }
        ],
        status: "pending",
        totalAmount: product.price * quantity,
        shippingAddress: user.address || {
          fullName: user.name,
          phone: user.phone,
          address: "",
          city: "",
          district: "",
          ward: ""
        },
        paymentMethod: "pending",
        createdAt: new Date().toISOString()
      }

      // Gửi request để thêm đơn hàng
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newOrder),
      })

      if (!response.ok) {
        throw new Error('Failed to add order')
      }

      // Cập nhật số lượng tồn kho trong products.json
      const updateStockResponse = await fetch(`/api/products/${product.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stock: product.stock - quantity
        }),
      })

      if (!updateStockResponse.ok) {
        throw new Error('Failed to update stock')
      }

      toast({
        title: "Thành công",
        description: "Đã thêm sản phẩm vào giỏ hàng!",
      })

      // Reset quantity về 1 sau khi thêm thành công
      setQuantity(1)
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast({
        title: "Lỗi",
        description: "Không thể thêm sản phẩm vào giỏ hàng. Vui lòng thử lại sau.",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  if (error || !product) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Không tìm thấy sản phẩm</h2>
            <p className="text-muted-foreground mb-4">
              {error || "Sản phẩm bạn đang tìm kiếm không tồn tại hoặc đã bị xóa."}
            </p>
            <Button asChild>
              <Link href="/products">Quay lại danh sách sản phẩm</Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout user={user || undefined}>
      <div className="container mx-auto px-4 py-8">
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Gallery */}
          <div className="space-y-4">
            <div className="relative aspect-square overflow-hidden rounded-lg bg-muted">
              <img
                src={product.images[selectedImage]}
                alt={product.name}
                className="object-cover w-full h-full"
              />
            </div>
            <div className="grid grid-cols-4 gap-4">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative aspect-square overflow-hidden rounded-lg bg-muted ${
                    selectedImage === index ? 'ring-2 ring-primary' : ''
                  }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} - ${index + 1}`}
                    className="object-cover w-full h-full"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{product.name}</h1>
              <div className="flex items-center gap-2 mt-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < Math.floor(product.rating)
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">
                  {product.rating} ({product.reviewCount} đánh giá)
                </span>
                <Separator orientation="vertical" className="h-4" />
                <span className="text-sm text-muted-foreground">
                  Đã bán {product.soldCount}
                </span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-baseline gap-2">
                <p className="text-3xl font-bold">{product.price.toLocaleString('vi-VN')}đ</p>
                {product.originalPrice > product.price && (
                  <p className="text-lg text-muted-foreground line-through">
                    {product.originalPrice.toLocaleString('vi-VN')}đ
                  </p>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Mô tả sản phẩm</h3>
                  <p className="text-muted-foreground">{product.description}</p>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Thông số kỹ thuật</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-muted-foreground">Danh mục</div>
                    <div>{product.category.name}</div>
                    <div className="text-muted-foreground">Tình trạng</div>
                    <div>
                      {product.stock > 0 ? (
                        <Badge variant="outline" className="bg-green-100 text-green-800">
                          Còn hàng
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-red-100 text-red-800">
                          Hết hàng
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleQuantityChange(quantity - 1)}
                      disabled={quantity <= 1}
                    >
                      -
                    </Button>
                    <Input
                      type="number"
                      min="1"
                      max={product.stock}
                      value={quantity}
                      onChange={(e) => handleQuantityChange(parseInt(e.target.value))}
                      className="w-16 text-center"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleQuantityChange(quantity + 1)}
                      disabled={quantity >= product.stock}
                    >
                      +
                    </Button>
                  </div>
                  <Button 
                    className="flex-1" 
                    onClick={handleAddToCart}
                    disabled={product.stock === 0}
                  >
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    {product.stock === 0 ? 'Hết hàng' : 'Thêm vào giỏ hàng'}
                  </Button>
                  <Button variant="outline" size="icon">
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
} 