import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const filePath = path.join(process.cwd(), 'data', 'orders.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const orders = JSON.parse(fileContents)
    const order = orders.find((o: any) => o.id === params.id)

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(order)
  } catch (error) {
    console.error('Error reading order:', error)
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const orderId = params.id

    // Đọc file orders.json
    const filePath = path.join(process.cwd(), 'data', 'orders.json')
    const fileContents = fs.readFileSync(filePath, 'utf8')
    const orders = JSON.parse(fileContents)

    // Tìm đơn hàng cần xóa
    const orderIndex = orders.findIndex((order: any) => order.id === orderId)
    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    // Kiểm tra trạng thái đơn hàng
    if (orders[orderIndex].status !== 'pending') {
      return NextResponse.json(
        { error: 'Cannot delete order that is not in pending status' },
        { status: 400 }
      )
    }

    // Xóa đơn hàng
    orders.splice(orderIndex, 1)

    // Lưu lại vào file
    fs.writeFileSync(filePath, JSON.stringify(orders, null, 2))

    return NextResponse.json({ message: 'Order deleted successfully' })
  } catch (error) {
    console.error('Error deleting order:', error)
    return NextResponse.json(
      { error: 'Failed to delete order' },
      { status: 500 }
    )
  }
} 